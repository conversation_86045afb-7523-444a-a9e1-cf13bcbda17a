import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
      meta: { title: '首页' }
    },
    {
      path: '/test',
      name: 'test',
      component: () => import('../views/TestView.vue'),
      meta: { title: '测试页面' }
    },
    {
      path: '/test-experiment-record',
      name: 'test-experiment-record',
      component: () => import('../views/TestExperimentRecordView.vue'),
      meta: { title: '实验记录表测试' }
    },
    {
      path: '/demo-experiment-record',
      name: 'demo-experiment-record',
      component: () => import('../views/ExperimentRecordDemo.vue'),
      meta: { title: '实验记录系统演示' }
    },
    {
      path: '/experiments/current-control-circuit',
      name: 'current-control-circuit',
      component: () => import('../experiments/CurrentControlCircuit.vue'),
      meta: { title: '制流电路实验' }
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/AdminView.vue'),
      meta: { title: '管理后台', requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { title: '个人信息', requiresAuth: true }
    }
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 大学物理实验基础指导平台`
  }

  next()
})

export default router
